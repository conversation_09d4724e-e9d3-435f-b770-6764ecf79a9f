[gd_scene load_steps=6 format=3 uid="uid://br8bwfdpj2fy7"]

[ext_resource type="Script" uid="uid://dummuimmbn6c5" path="res://built in gates (deprecated, to be deleted soon)/output/output.gd" id="1_bufa6"]
[ext_resource type="PackedScene" uid="uid://es2mohaxdhi" path="res://classes/pin 2d/pin_2d.tscn" id="1_x75ch"]
[ext_resource type="Theme" uid="uid://bhsrstq1b653s" path="res://theme/default theme.tres" id="2_3xq57"]

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_x75ch"]
radius = 16.0
height = 36.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_x75ch"]
bg_color = Color(1, 1, 1, 1)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[node name="q" type="StaticBody2D" groups=["output"]]
script = ExtResource("1_bufa6")

[node name="input pins" type="Node2D" parent="."]

[node name="Pin 2D" parent="input pins" instance=ExtResource("1_x75ch")]
position = Vector2(-28, 0)

[node name="Line2D" parent="input pins/Pin 2D" index="4"]
points = PackedVector2Array(0, 0, 20, 0)

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
rotation = 1.5708
shape = SubResource("CapsuleShape2D_x75ch")

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 0
offset_right = 40.0
offset_bottom = 40.0

[node name="PanelContainer" type="PanelContainer" parent="Control"]
modulate = Color(0.160784, 0.662745, 0.803922, 1)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -39.0
offset_top = -36.0
offset_right = -1.0
offset_bottom = -4.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_x75ch")

[node name="LineEdit" type="LineEdit" parent="."]
offset_left = -15.0
offset_top = -11.0
offset_right = 14.0
offset_bottom = 12.0
theme = ExtResource("2_3xq57")
text = "q"
alignment = 1

[editable path="input pins/Pin 2D"]
