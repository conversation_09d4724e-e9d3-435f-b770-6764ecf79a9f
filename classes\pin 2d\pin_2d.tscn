[gd_scene load_steps=4 format=3 uid="uid://es2mohaxdhi"]

[ext_resource type="Script" uid="uid://cshgrh58u7g14" path="res://classes/pin 2d/pin_2d.gd" id="1_7772h"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_7772h"]
bg_color = Color(1, 1, 1, 1)
corner_radius_top_left = 100
corner_radius_top_right = 100
corner_radius_bottom_right = 100
corner_radius_bottom_left = 100

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_7772h"]
height = 20.0

[node name="Pin 2D" type="StaticBody2D" groups=["pin2d"]]
script = ExtResource("1_7772h")
color_default = Color(0.423651, 0.423651, 0.423651, 1)
color_connected = Color(1, 1, 1, 1)
color_true = Color(0.427451, 1, 0.407843, 1)

[node name="Outline" type="PanelContainer" parent="."]
visible = false
modulate = Color(1, 1, 0.0431373, 1)
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -9.0
offset_top = -9.0
offset_right = 9.0
offset_bottom = 9.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_7772h")

[node name="PanelContainer" type="PanelContainer" parent="."]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -5.0
offset_top = -5.0
offset_right = 5.0
offset_bottom = 5.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_7772h")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
rotation = 1.5708
shape = SubResource("CapsuleShape2D_7772h")

[node name="Wire Detector" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Wire Detector"]
rotation = 1.5708
shape = SubResource("CapsuleShape2D_7772h")

[node name="Line2D" type="Line2D" parent="."]
z_index = -2
points = PackedVector2Array(0, 0)
width = 5.0
default_color = Color(0.552956, 0.552956, 0.552956, 1)
