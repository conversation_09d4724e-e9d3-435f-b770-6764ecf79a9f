class_name GateLibrary extends CanvasLayer

#region Load All Gates
@onready var tab_container = $"Control/Control/TabContainer"
@onready var basic_gate_container = $"Control/Control/TabContainer/Basic/Basic"
@onready var advanced_gate_container = $"Control/Control/TabContainer/Advanced/Advanced"
@onready var custom_gate_container = $"Control/Control/TabContainer/Custom/Custom"

func _get_all_basic_gates():
	var file = FileAccess.open("res://built in gates/basic gates/basic gates.json", FileAccess.READ)
	if file:
		var json = JSON.new()
		json.parse(file.get_as_text())
		return json.data
	return []

func _get_all_advanced_gates():
	var file = FileAccess.open("res://built in gates/advanced gates/advanced gates.json", FileAccess.READ)
	if file:
		var json = JSON.new()
		json.parse(file.get_as_text())
		return json.data
	return []

func _get_all_custom_gates():
	return SaveSystem.custom_gates["custom_gates"]

func _ready():
	# create buttons for Switch, Output, and Clock
	for gate in basic_gates:
		var g = gate.instantiate()
		var button = Button.new()
		button.text = g.name
		button.theme = load("res://theme/gate library theme.tres")
		button.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
		button.modulate = g.get_node("Control/PanelContainer").modulate
		if g is Switch:
			button.text = "Switch"
			button.connect("button_down", _on_gate_button_clicked.bind({}, gate))
		elif g is Output:
			button.text = "Output"
			button.connect("button_down", _on_gate_button_clicked.bind({}, gate))
		elif g is CLK:
			button.text = "CLK"
			button.connect("button_down", _on_gate_button_clicked.bind({}, gate))
		basic_gate_container.add_child(button)

	# create buttons for each gate
	for gate in _get_all_basic_gates():
		var button = Button.new()
		button.text = gate.gate_name
		button.theme = load("res://theme/gate library theme.tres")
		button.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
		button.modulate = _get_gate_color(gate.get("color", "(1, 1, 1, 1)"))
		button.connect("button_down", _on_gate_button_clicked.bind(gate))
		basic_gate_container.add_child(button)
	
	for gate in _get_all_advanced_gates():
		var button = Button.new()
		button.text = gate.gate_name
		button.theme = load("res://theme/gate library theme.tres")
		button.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
		button.modulate = _get_gate_color(gate.get("color", "(1, 1, 1, 1)"))
		button.connect("button_down", _on_gate_button_clicked.bind(gate))
		advanced_gate_container.add_child(button)
	
	for gate in _get_all_custom_gates():
		var button = Button.new()
		button.text = gate.gate_name
		button.theme = load("res://theme/gate library theme.tres")
		button.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
		button.modulate = _get_gate_color(gate.get("color", "(1, 1, 1, 1)"))
		button.connect("button_down", _on_gate_button_clicked.bind(gate))
		custom_gate_container.add_child(button)
	
	# for gate in advanced_gates:
	# 	var g = gate.instantiate()
	# 	var button = Button.new()
	# 	button.text = g.name
	# 	button.theme = load("res://theme/gate library theme.tres")
	# 	button.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
	# 	button.modulate = g.get_node("Control/PanelContainer").modulate
	# 	var gate_data = {
	# 		"gate_name": g.gate_name,
	# 		"gate_inputs": g.input_pins,
	# 		"gate_outputs": g.output_pins,
	# 		"boolean_expressions": g.boolean_expressions
	# 	}
	# 	if not g.is_in_group("special_gates"):
	# 		button.connect("button_down", _on_gate_button_clicked.bind(gate_data))
	# 	else:
	# 		button.connect("button_down", _on_gate_button_clicked.bind({}, gate))
	# 	advanced_gate_container.add_child(button)

	# var custom_gates = SaveSystem.custom_gates["custom_gates"]
	# for gate in custom_gates:
	# 	var button = Button.new()
	# 	button.text = gate.gate_name
	# 	button.theme = load("res://theme/gate library theme.tres")
	# 	button.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
	# 	button.modulate = _get_gate_color(gate.color)
	# 	button.connect("button_down", _on_gate_button_clicked.bind(gate))
	# 	custom_gate_container.add_child(button)

func _get_gate_color(color_str: String):
	var color_array = color_str.replace("(", "").replace(")", "").split(", ")
	var color = Color(float(color_array[0]), float(color_array[1]), float(color_array[2]), float(color_array[3]))
	return color
#endregion

#region Drag Gates Into Canvas
@export var basic_gates: Array[PackedScene]
@export var advanced_gates: Array[PackedScene]

@onready var canvas_node: CanvasNode = get_parent().get_node("Canvas Node")

func _on_gate_button_clicked(gate_data: Dictionary, switch_or_output: PackedScene = null):
	var g = null
	if switch_or_output:
		# instantiate the switch if available
		g = switch_or_output.instantiate()
		if g is Switch:
			g.name = "Switch"
		g.global_position = canvas_node.get_snapped_detector_position()
		get_parent().add_child(g)
	else:
		var i: Dictionary[String, bool] = {}
		for input in gate_data.gate_inputs:
			i[input] = false
		var o: Dictionary[String, bool] = {}
		for output in gate_data.gate_outputs:
			o[output] = false
		# instantiate the gate
		g = preload("res://classes/gate 2d/gate_2d.tscn").instantiate()
		g.add_to_group("gate2d")
		g.name = gate_data.gate_name
		g.gate_name = gate_data.gate_name
		g.input_pins = i
		g.output_pins = o
		g.nodes = gate_data.nodes
		g.global_position = canvas_node.get_snapped_detector_position()
		g.gate_color = _get_gate_color(gate_data.get("color", "(1, 1, 1, 1)"))
		# add the gate to the canvas
		get_parent().add_child(g)

	# set the dragged gate immediately to the newly added gate
	canvas_node.dragged_gate = g

	# animate the gate to be larger
	var tween = get_tree().create_tween().set_trans(Tween.TRANS_BOUNCE).set_ease(Tween.EASE_OUT)
	tween.tween_property(g, "scale", Vector2.ONE * canvas_node.gate_dragged_size, 0.1)

	# put the canvas in drag mode
	canvas_node.is_dragging_gate = true 

func _on_open_gate_library_button_button_down():
	# close the gate library
	canvas_node.can_interact = false
	var tween = get_tree().create_tween().set_trans(Tween.TRANS_SINE).set_ease(Tween.EASE_IN_OUT)
	var is_open = $Control.position.x != 0
	if is_open:
		tween.tween_property($Control, "position:x", 0, 0.5)
		await tween.finished
	else:
		tween.tween_property($Control, "position:x", 390, 0.5)
		await tween.finished
	canvas_node.can_interact = true
	canvas_node.is_panning = false

#endregion

#region Save Custom Gate
@onready var confirm_save_container = $"Save Gate/PanelContainer"
@onready var gate_name_input = $"Save Gate/PanelContainer/VBoxContainer/LineEdit"
@onready var anim_player = $AnimationPlayer
@onready var color_picker = $"Save Gate/PanelContainer/VBoxContainer/HBoxContainer2/HBoxContainer/ColorPickerButton"

# show the save gate menu
func _on_save_gate_button_button_down():
	confirm_save_container.visible = true
	anim_player.play("show save menu")

# hide save gate menu
func _on_back_pressed():
	anim_player.play_backwards("show save menu")
	await anim_player.animation_finished
	confirm_save_container.visible = false

# save the custom gate
func _on_confirm_safe_button_pressed():
	# var boolean_expressions = {}

	# # generate the boolean expression for each output pin and save it to the dictionary
	# for output in get_tree().get_nodes_in_group("output"):
	# 	boolean_expressions[output.name] = output.generate_boolean_expression_of_current_canvas()

	for output in get_tree().get_nodes_in_group("output"):
		output.convert_prev_gate_to_node()
	
	print(_get_input_pins_in_canvas())
	print(_get_output_pins_in_canvas())
	print(SimulationManager.nodes)

	# create the gate data
	var gate_data = {
		"gate_name": gate_name_input.text,
		"gate_inputs": _get_input_pins_in_canvas(),
		"gate_outputs": _get_output_pins_in_canvas(),
		"color": color_picker.color,
		"nodes": SimulationManager.nodes
	}
	# save the gate data
	SaveSystem.add_custom_gate_and_save(gate_data)
	print(SaveSystem.custom_gates)

	# hide save gate menu
	anim_player.play_backwards("show save menu")
	await anim_player.animation_finished
	confirm_save_container.visible = false

func _get_input_pins_in_canvas():
	var res = []
	for child in get_tree().get_nodes_in_group("switch") + get_tree().get_nodes_in_group("clk"):
		res.append(child.name)
	return res

func _get_output_pins_in_canvas():
	var res = []
	for child in get_tree().get_nodes_in_group("output"):
		res.append(child.name)
	return res
#endregion
