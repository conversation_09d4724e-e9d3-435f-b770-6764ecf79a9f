[gd_scene load_steps=3 format=3 uid="uid://b5uk1vsfip84c"]

[ext_resource type="Script" uid="uid://crbhw0bbwrvq3" path="res://canvas node/canvas_node.gd" id="1_gfpb1"]

[sub_resource type="CircleShape2D" id="CircleShape2D_jigk7"]

[node name="Canvas Node" type="Node2D"]
script = ExtResource("1_gfpb1")
dot_color = Color(0.197937, 0.197937, 0.197937, 1)
pan_sensitivity = 0.7
zoom_sensitivity = 0.5
gate_dragged_size = 1.1

[node name="Camera2D" type="Camera2D" parent="."]
position = Vector2(2, 0)

[node name="Detector" type="Area2D" parent="."]
position = Vector2(287, 96)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Detector"]
shape = SubResource("CircleShape2D_jigk7")
