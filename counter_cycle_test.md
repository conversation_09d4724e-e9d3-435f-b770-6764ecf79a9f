# Counter Cycle Detection Test

## The Problem You Identified

You were absolutely correct! The issue was that when building a counter:

1. **Physical Cycles**: Q output of each D flip-flop connects back to D input (or to next flip-flop)
2. **Multiple Gates**: Each flip-flop is a separate gate, but they're interconnected
3. **Incorrect Cycle Detection**: The old system only preserved one gate, causing all cycles to be treated as "g"
4. **Gate Saving Issues**: When saving the counter as a custom gate, it generated expressions with only "g" variables instead of unique identifiers

## The Fix: Network-Wide Cycle Detection

### Before (Problematic):
```gdscript
# When saving a 4-bit counter, all cycles were treated the same:
"q0": "some_expression_with_g"
"q1": "another_expression_with_g"  # Same "g" - WRONG!
"q2": "yet_another_expression_with_g"  # Same "g" - WRONG!
"q3": "final_expression_with_g"  # Same "g" - WRONG!
```

### After (Fixed):
```gdscript
# Now each gate gets unique cycle variables:
"q0": "some_expression_with_cycle_12345_q0"
"q1": "expression_with_cycle_12346_q1_and_cycle_12345_q0"
"q2": "expression_with_cycle_12347_q2_and_cycle_12346_q1"
"q3": "expression_with_cycle_12348_q3_and_cycle_12347_q2"
```

## Key Changes Made:

### 1. Network-Wide Cycle Detection
```gdscript
func generate_complete_boolean_expression(gate_to_preserve: Gate2D = null) -> Dictionary:
    # If no gate specified, use network-wide cycle detection
    if gate_to_preserve == null:
        return _generate_expression_with_network_cycles()
    else:
        return _generate_expression_recursive(gate_to_preserve, [])
```

### 2. Unique Cycle Variables
```gdscript
func _get_network_replacement(source_gate, source_pin_name: String, visited_gates: Dictionary, cycle_map: Dictionary) -> String:
    if source_gate is Gate2D:
        var gate_id = str(source_gate.get_instance_id())
        
        if visited_gates.has(gate_id):
            # Create unique cycle variable: cycle_[gate_id]_[pin_name]
            var cycle_var = "cycle_" + gate_id + "_" + source_pin_name
            cycle_map[cycle_var] = {"gate": source_gate, "pin": source_pin_name}
            return cycle_var
```

### 3. Proper Variable Resolution
```gdscript
func _gather_unique_cycle_variables_from_network() -> Dictionary:
    # Creates cycle_[gate_id]_[pin_name] variables for all gates
    # Each gate's previous state is uniquely identified
```

## How This Fixes Your Counter:

### Counter Construction Scenario:
1. **DFF1**: Q connects to DFF2's D input
2. **DFF2**: Q connects to DFF3's D input  
3. **DFF3**: Q connects to DFF4's D input
4. **DFF4**: Q connects back to DFF1's D input (for ripple counter)

### Old Behavior:
- All connections treated as same "g" variable
- Expressions couldn't distinguish between different flip-flops
- Result: `null` because variables were undefined or conflicting

### New Behavior:
- **DFF1**: Uses `cycle_[DFF4_ID]_q` for its D input (from DFF4's Q)
- **DFF2**: Uses `cycle_[DFF1_ID]_q` for its D input (from DFF1's Q)
- **DFF3**: Uses `cycle_[DFF2_ID]_q` for its D input (from DFF2's Q)
- **DFF4**: Uses `cycle_[DFF3_ID]_q` for its D input (from DFF3's Q)

### Expression Evaluation:
- All `cycle_[ID]_[pin]` variables are available in the evaluation context
- Each variable contains the previous state of the specific gate/pin
- No more `null` results because all variables are properly defined

## Testing Your Counter:

1. **Build a Counter**: Connect D flip-flops in a counter configuration
2. **Save as Custom Gate**: Use the gate library to save the counter
3. **Check Generated Expressions**: Should now have unique cycle variables instead of all "g"
4. **Test Evaluation**: `res_i` should no longer be `null`

## Example Output:

For a 2-bit counter made of two D flip-flops:

```gdscript
# DFF1 (LSB):
"q": "(clk_signal and cycle_67890_q) or (not clk_signal and cycle_12345_q)"
"qn": "not ((clk_signal and cycle_67890_q) or (not clk_signal and cycle_12345_q))"

# DFF2 (MSB):  
"q": "(clk_signal and cycle_12345_q and cycle_67890_q) or (not clk_signal and cycle_67891_q)"
"qn": "not ((clk_signal and cycle_12345_q and cycle_67890_q) or (not clk_signal and cycle_67891_q))"
```

Where:
- `cycle_12345_q` = previous state of DFF1's Q output
- `cycle_67890_q` = previous state of some other connected gate
- `cycle_67891_q` = previous state of DFF2's Q output

This should completely resolve the `null` result issue with your counters!
