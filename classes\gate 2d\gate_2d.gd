class_name Gate2D extends StaticBody2D

#region Render Gate
@export_group("General Gate Information")
@export var gate_name: String = "Gate"
@export var input_pins: Dictionary[String, bool]
@export var output_pins: Dictionary[String, bool]
@export var nodes: Array

@export_group("Rendering")
@export var distance_btw_pins: int = 32
@export var gate_color: Color

@onready var input_pin_container = $"input pins"
@onready var output_pin_container = $"output pins"
@onready var label = $Control/Label
@onready var color_rect = $"Control/PanelContainer"
@onready var canvas_node: CanvasNode = get_parent().get_node("Canvas Node")
@onready var pin_scene = preload("res://classes/pin 2d/pin_2d.tscn")

func _ready():
	# create a copy of input and output pins
	input_pins = input_pins.duplicate()
	output_pins = output_pins.duplicate()

	# set the display name for the gate
	label.text = gate_name

	# set the size of the color rect, which is the body of the gate, based on the number of input and output pins
	color_rect.size.x = max(label.get_minimum_size().x, 100)
	color_rect.size.y = max(input_pins.size() + 1, output_pins.size() + 1) * distance_btw_pins
	color_rect.position.x = (color_rect.size.x - 40) / -2
	color_rect.position.y = (color_rect.size.y - distance_btw_pins - 5) / -2

	# set the size of the collision shape to the size of the color rect
	$CollisionShape2D.shape.size = color_rect.size - Vector2.ONE * 50

	# update the color of the color rect
	color_rect.modulate = gate_color

	# update the horizontal position of the input and output pins
	input_pin_container.position.x = color_rect.position.x - 30
	input_pin_container.position = canvas_node.snap_to_grid(input_pin_container.position)
	output_pin_container.position.x = color_rect.position.x + color_rect.size.x
	output_pin_container.position = canvas_node.snap_to_grid(output_pin_container.position)

	# create the input pins
	var pos_shift_input = floor(float(input_pins.size()) / 2)
	for i in range(input_pins.size()):
		# create the pin and position it accordingly
		var pin: Pin2D = pin_scene.instantiate()
		pin.is_input_pin = true
		pin.name = input_pins.keys()[i]
		if input_pins.size() % 2 == 0 and i >= pos_shift_input:
			pin.position.y = (i-pos_shift_input+1) * distance_btw_pins
		else:
			pin.position.y = (i-pos_shift_input) * distance_btw_pins
		pin.position = canvas_node.snap_to_grid(pin.position)
		input_pin_container.add_child(pin)

		# create the label for the pin and position it accordingly
		var new_label: Label = Label.new()
		new_label.text = input_pins.keys()[i]
		new_label.position = Vector2(-color_rect.size.x / 2 - 20, pin.position.y - 8)
		new_label.theme = load("res://theme/default theme.tres")
		new_label.set("theme_override_font_sizes/font_size", 10)
		new_label.modulate = Color.BLACK
		add_child(new_label)

		# connect the pin to the label with a Line2D
		(pin.get_node("Line2D") as Line2D).add_point(Vector2.RIGHT * 30)

	# create the output pins
	var pos_shift_output = floor(float(output_pins.size()) / 2)
	for i in range(output_pins.size()):
		# create the pin and position it accordingly
		var pin: Pin2D = pin_scene.instantiate()
		pin.name = output_pins.keys()[i]
		if output_pins.size() % 2 == 0 and i >= pos_shift_output:
			pin.position.y = (i-pos_shift_output+1) * distance_btw_pins
		else:
			pin.position.y = (i-pos_shift_output) * distance_btw_pins
		pin.position = canvas_node.snap_to_grid(pin.position)
		output_pin_container.add_child(pin)

		# create the label for the pin and position it accordingly
		var new_label: Label = Label.new()
		new_label.text = output_pins.keys()[i]
		new_label.position = Vector2(color_rect.size.x / 2 - 55, pin.position.y - 8)
		new_label.theme = load("res://theme/default theme.tres")
		new_label.set("theme_override_font_sizes/font_size", 10)
		new_label.modulate = Color.BLACK
		add_child(new_label)

		# connect the pin to the label with a Line2D
		(pin.get_node("Line2D") as Line2D).add_point(Vector2.LEFT * 30)
#endregion

#region Evaluate Expression
var current_output_for_each_pin: Dictionary[String, bool]
var last_clk_state = false
var wires = {} # stores intermediate values

func evaluate_expression():
	var can_evaluate_expression = not _has_clock()

	if _has_clock():
		can_evaluate_expression = _is_positive_edge_triggered()
		last_clk_state = input_pins.get("clk", false)

	if can_evaluate_expression:
		# initialize the wires
		for node in nodes:
			wires[node["output"]] = false
			for inp in node["inputs"]:
				if inp not in wires and inp not in input_pins:
					wires[inp] = false
		for inp in input_pins:
			wires[inp] = false
		for out in output_pins:
			wires[out] = current_output_for_each_pin.get(out, false)

		# initialize the inputs
		for pin in input_pin_container.get_children():
			wires[pin.name] = pin.state

		# evaluate the expression
		_step()

		# update the output pins
		for pin in output_pin_container.get_children():
			current_output_for_each_pin[pin.name] = wires[pin.name]
			pin.state = wires[pin.name]
			await pin.transfer_state()

# func evaluate_expression():
# 	var res = {}

# 	var can_evaluate_expression = not _has_clock()

# 	if _has_clock():
# 		can_evaluate_expression = _is_positive_edge_triggered()
# 		last_clk_state = input_pins.get("clk", false)

# 	if can_evaluate_expression:
# 		# evaluate the boolean expression for each output pin
# 		for pin in output_pin_container.get_children():
# 			var expr = Expression.new()

# 			# Prepare variables for expression evaluation
# 			var variables = input_pins.duplicate()

# 			# Add legacy "g" variable for backward compatibility
# 			variables["g"] = current_output_for_each_pin.get(pin.name, false)

# 			# Add cycle variables (for future use with improved cycle detection)
# 			# This allows expressions to reference previous states of gates in feedback loops
# 			var cycle_vars = _get_cycle_variables_for_evaluation()
# 			for var_name in cycle_vars.keys():
# 				variables[var_name] = cycle_vars[var_name]

# 			var err = expr.parse(boolean_expressions[pin.name], variables.keys())
# 			if err != OK:
# 				print("Error parsing expression: ", err)
# 				return false
# 			var res_i = expr.execute(variables.values())
# 			current_output_for_each_pin[pin.name] = res_i
# 			pin.state = res_i
# 			await pin.transfer_state()
# 			res[pin.name] = res_i

# 	return res

func _eval_node(node: Dictionary):
	var t = node["type"]
	var ins = []
	for i in node["inputs"]:
		ins.append(wires[i])

	match t:
		"AND":
			return not ins.has(false)
		"OR":
			return ins.has(true)
		"NOT":
			return not ins[0]
		"XOR":
			return ins[0] != ins[1]
		"NOR":
			return not ins.has(true)
		_:
			push_error("Unknown node type: " + t)
			return false

func _step(max_iter=10):
	for i in range(max_iter):
		var changed = false
		for node in nodes:
			var new_val = _eval_node(node)
			if wires[node["output"]] != new_val:
				wires[node["output"]] = new_val
				changed = true
		if not changed:
			break

func _has_clock():
	return input_pins.has("clk")

func _is_positive_edge_triggered():
	return input_pins.get("clk", false) and not last_clk_state
#endregion

#region Convert To Node
func convert_to_node(output_swaps: Dictionary = {}, visited_gates: Dictionary = {}):
	# Use unique gate ID for tracking
	var gate_id = str(get_instance_id())

	# Check if we've already processed this gate (cycle detection)
	if visited_gates.has(gate_id):
		# This is a cycle - return a reference to the already processed nodes
		# Return all the output names that were assigned to this gate when it started processing
		var assigned_outputs = visited_gates[gate_id]
		return {"is_cycle": true, "output_names": assigned_outputs}

	# Check if this gate was already converted in a previous call
	if SimulationManager.converted_node_id.has(gate_id):
		# Find the existing nodes and return their output names
		var existing_outputs = {}
		for node in SimulationManager.nodes:
			if node["id"].begins_with(gate_id):
				var output_pin = node["output_pin"] if node.has("output_pin") else ""
				existing_outputs[output_pin] = node["output"]
		return {"is_cycle": false, "output_names": existing_outputs}

	# Determine which outputs to create nodes for
	var outputs_to_process = {}
	if output_swaps.size() > 0:
		# Use the provided output swaps
		outputs_to_process = output_swaps
	else:
		# Create nodes for all output pins, using their pin names as output names
		for output_pin_name in output_pins.keys():
			var real_output_name = _find_real_output_name_for_gate_pin(self, output_pin_name)
			if real_output_name != "":
				outputs_to_process[output_pin_name] = real_output_name
			else:
				outputs_to_process[output_pin_name] = output_pin_name

	# Mark this gate as being processed (for cycle detection)
	# Store the output names so cycles can reference them correctly
	visited_gates[gate_id] = outputs_to_process

	var created_nodes = []

	# Check if this gate has internal nodes (custom gate) that need to be expanded
	if nodes.size() > 0:
		# This is a custom gate - expand its internal nodes instead of creating a single node
		return _expand_custom_gate_nodes(outputs_to_process, visited_gates)

	# Create a separate node for each output (for primitive gates)
	for output_pin_name in outputs_to_process.keys():
		var output_name = outputs_to_process[output_pin_name]

		var res = {}
		res.id = gate_id + "_" + output_pin_name  # Unique ID for each output node
		res.type = gate_name
		res.output = output_name
		res.output_pin = output_pin_name  # Track which pin this node represents
		res.inputs = []

		# Process each input pin for this node
		for inp in input_pin_container.get_children():
			if inp.get_connected_wires().size() == 0:
				# No connection - skip this input
				continue

			var connected_wire = inp.get_connected_wires()[0]
			var prev_gate = connected_wire.input_pin.get_parent().get_parent()

			if prev_gate is Gate2D:
				# Check if this previous gate connects to an output node to get the real output name
				var prev_output_swaps = {}
				var real_output_name = _find_real_output_name_for_gate(prev_gate)
				if real_output_name != "":
					# Find which output pin connects to this real output
					for prev_output_pin in prev_gate.output_pins.keys():
						var prev_real_name = _find_real_output_name_for_gate_pin(prev_gate, prev_output_pin)
						if prev_real_name == real_output_name:
							prev_output_swaps[prev_output_pin] = real_output_name
							break
				else:
					# No direct output connection, use intermediate wire name
					var intermediate_name = inp.name + "_" + res.id
					var source_pin_name = connected_wire.input_pin.name
					prev_output_swaps[source_pin_name] = intermediate_name

				# Recursively convert the previous gate
				var conversion_result = prev_gate.convert_to_node(prev_output_swaps, visited_gates)

				if conversion_result != null:
					if conversion_result.has("is_cycle") and conversion_result["is_cycle"]:
						# This is a cycle reference - find the appropriate output
						var cycle_outputs = conversion_result["output_names"]
						var source_pin_name = connected_wire.input_pin.name
						if cycle_outputs.has(source_pin_name):
							res.inputs.append(cycle_outputs[source_pin_name])
						else:
							# Fallback to first available output
							res.inputs.append(cycle_outputs.values()[0])
					else:
						# Normal conversion - find the appropriate output
						var normal_outputs = conversion_result["output_names"]
						var source_pin_name = connected_wire.input_pin.name
						if normal_outputs.has(source_pin_name):
							res.inputs.append(normal_outputs[source_pin_name])
						else:
							# Fallback to first available output
							res.inputs.append(normal_outputs.values()[0])
				else:
					# Fallback for failed conversion
					res.inputs.append("false")
			elif prev_gate is Switch:
				res.inputs.append(prev_gate.name)
			else:
				# Unknown gate type - use a default value
				res.inputs.append("false")

		created_nodes.append(res)

	# Remove this gate from visited (we're done processing it)
	visited_gates.erase(gate_id)

	# Add all created nodes to the global nodes list and mark as converted
	for node in created_nodes:
		SimulationManager.nodes.append(node)
	SimulationManager.converted_node_id.append(gate_id)

	return {"is_cycle": false, "output_names": outputs_to_process}

# Helper function to find the real output name for a gate by checking if it connects to an Output node
func _find_real_output_name_for_gate(gate: Gate2D) -> String:
	# Check all output pins of the gate
	for output_pin in gate.output_pin_container.get_children():
		# Check all wires connected to this output pin
		for wire in output_pin.get_connected_wires():
			if wire.output_pin:
				var connected_node = wire.output_pin.get_parent().get_parent()
				# Check if it's connected to an Output node
				if connected_node.is_in_group("output"):
					return connected_node.name
	return ""

# Helper function to find the real output name for a specific output pin of a gate
func _find_real_output_name_for_gate_pin(gate: Gate2D, pin_name: String) -> String:
	# Find the specific output pin
	for output_pin in gate.output_pin_container.get_children():
		if output_pin.name == pin_name:
			# Check all wires connected to this specific output pin
			for wire in output_pin.get_connected_wires():
				if wire.output_pin:
					var connected_node = wire.output_pin.get_parent().get_parent()
					# Check if it's connected to an Output node
					if connected_node.is_in_group("output"):
						return connected_node.name
	return ""

# Helper function to expand custom gates into their constituent primitive nodes
func _expand_custom_gate_nodes(outputs_to_process: Dictionary, _visited_gates: Dictionary) -> Dictionary:
	var gate_id = str(get_instance_id())
	var expanded_nodes = []

	# Create a mapping from internal node outputs to final outputs
	var internal_to_final_output = {}

	# Map the gate's output pins to the final output names
	for output_pin_name in outputs_to_process.keys():
		var final_output_name = outputs_to_process[output_pin_name]
		# Find which internal node produces this output
		for internal_node in nodes:
			if internal_node["output"] == output_pin_name:
				internal_to_final_output[internal_node["output"]] = final_output_name
				break

	# Process each internal node
	for i in range(nodes.size()):
		var internal_node = nodes[i]
		var expanded_node = {}

		# Create unique ID for this expanded node
		expanded_node.id = gate_id + "_internal_" + str(i)
		expanded_node.type = internal_node["type"]

		# Determine the output name for this node
		if internal_to_final_output.has(internal_node["output"]):
			# This internal node connects to a final output
			expanded_node.output = internal_to_final_output[internal_node["output"]]
		else:
			# This is an intermediate node
			expanded_node.output = gate_id + "_" + internal_node["output"]

		# Process inputs for this internal node
		expanded_node.inputs = []
		for input_name in internal_node["inputs"]:
			if input_pins.has(input_name):
				# This input comes from the gate's input pins
				expanded_node.inputs.append(_get_input_for_pin(input_name))
			else:
				# This input comes from another internal node
				var internal_output_name = gate_id + "_" + input_name
				expanded_node.inputs.append(internal_output_name)

		expanded_nodes.append(expanded_node)

	# Add all expanded nodes to the global list
	for node in expanded_nodes:
		SimulationManager.nodes.append(node)

	# Mark this gate as converted
	SimulationManager.converted_node_id.append(gate_id)

	return {"is_cycle": false, "output_names": outputs_to_process}

# Helper function to get the actual input source for a pin
func _get_input_for_pin(pin_name: String) -> String:
	# Find the input pin with this name
	for inp in input_pin_container.get_children():
		if inp.name == pin_name:
			if inp.get_connected_wires().size() > 0:
				var connected_wire = inp.get_connected_wires()[0]
				var prev_gate = connected_wire.input_pin.get_parent().get_parent()

				if prev_gate is Gate2D:
					# Get the output name from the previous gate
					var real_output_name = _find_real_output_name_for_gate(prev_gate)
					if real_output_name != "":
						return real_output_name
					else:
						# Use intermediate wire name
						return pin_name + "_" + str(get_instance_id())
				elif prev_gate is Switch:
					return prev_gate.name
				else:
					return "false"
			else:
				return "false"
	return "false"
#endregion

#region Generate Expression
# generates a complete boolean expression for the entire canvas (to be saved and made into custom gate)
func generate_complete_boolean_expression(_gate_to_preserve: Gate2D = null) -> Dictionary:
	# Use a simpler approach that avoids recursion issues
	return _generate_expression_iterative()

# Iterative expression generation to avoid stack overflow
func _generate_expression_iterative() -> Dictionary:
	var complete_boolean_expressions = {}

	# for output_pin_name in output_pins.keys():
	# 	var curr_expression = boolean_expressions.get(output_pin_name, "")
	# 	if curr_expression.is_empty():
	# 		complete_boolean_expressions[output_pin_name] = "false"
	# 		continue

	# 	# Process the expression by replacing input pins with appropriate values
	# 	var processed_expression = _replace_pins_iterative(curr_expression)
	# 	complete_boolean_expressions[output_pin_name] = processed_expression

	return complete_boolean_expressions

# Replace pins iteratively to avoid recursion
func _replace_pins_iterative(expression: String) -> String:
	var result = expression
	var tokens = _tokenize_expression(expression)
	var split_result = result.split(" ")

	# Replace each input pin token
	for pin in input_pin_container.get_children():
		var pin_name = pin.name

		# Check if this pin name exists as a token
		if pin_name in tokens and len(pin.get_connected_wires()) > 0:
			var connected_wire = pin.get_connected_wires()[0]
			var source_gate = connected_wire.input_pin.get_parent().get_parent()
			var source_pin_name = connected_wire.input_pin.name

			var replacement = _get_simple_replacement(source_gate, source_pin_name)
			for i in range(split_result.size()):
				if split_result[i] == pin_name:
					split_result[i] = replacement
			result = " ".join(split_result)

	return result

# Get simple replacement without recursion
func _get_simple_replacement(source_gate, source_pin_name: String) -> String:
	# Handle switch inputs
	if source_gate is Switch:
		return str(source_gate.name)

	# Handle CLK inputs
	if source_gate is CLK:
		return "clk_signal"

	# Handle Gate2D inputs - use cycle variables for all connections
	if source_gate is Gate2D:
		var gate_id = str(source_gate.get_instance_id())
		# Always use cycle variables to avoid recursion
		return "cycle_" + gate_id + "_" + source_pin_name

	# Fallback for unknown gate types
	return "false"

# Tokenize expression to identify variable names
func _tokenize_expression(expression: String) -> Array:
	var tokens = []

	for i in expression.split(" "):
		if not i in ["and", "or", "not", "(", ")", ""]:
			tokens.append(i)
	
	return tokens


#endregion
