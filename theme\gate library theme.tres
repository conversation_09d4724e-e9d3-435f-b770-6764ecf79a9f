[gd_resource type="Theme" load_steps=10 format=3 uid="uid://dpf0xnga20efv"]

[ext_resource type="FontFile" uid="uid://berob6jst1snq" path="res://fonts/Poppins/Poppins-Bold.ttf" id="1_2drlv"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_a6riu"]
content_margin_left = 50.0
content_margin_top = 20.0
content_margin_right = 50.0
content_margin_bottom = 20.0
bg_color = Color(1, 1, 1, 1)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_2drlv"]
content_margin_left = 50.0
content_margin_top = 20.0
content_margin_right = 50.0
content_margin_bottom = 20.0
bg_color = Color(1, 1, 1, 1)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_gyv4m"]
content_margin_left = 50.0
content_margin_top = 20.0
content_margin_right = 50.0
content_margin_bottom = 20.0
bg_color = Color(1, 1, 1, 1)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_2a2dh"]
content_margin_left = 50.0
content_margin_top = 20.0
content_margin_right = 50.0
content_margin_bottom = 20.0
bg_color = Color(1, 1, 1, 1)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_5py8f"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.0666667, 0.0666667, 0.0666667, 1)
corner_radius_top_left = 10
corner_radius_top_right = 10

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_p38iw"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0, 0, 0, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_anaxc"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.0666667, 0.0666667, 0.0666667, 1)
corner_radius_top_left = 10
corner_radius_top_right = 10

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_otsmp"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0, 0, 0, 1)

[resource]
Button/colors/font_color = Color(0, 0, 0, 1)
Button/colors/font_focus_color = Color(0, 0, 0, 1)
Button/colors/font_hover_color = Color(0, 0, 0, 1)
Button/colors/font_pressed_color = Color(0, 0, 0, 1)
Button/fonts/font = ExtResource("1_2drlv")
Button/styles/focus = SubResource("StyleBoxFlat_a6riu")
Button/styles/hover = SubResource("StyleBoxFlat_2drlv")
Button/styles/normal = SubResource("StyleBoxFlat_gyv4m")
Button/styles/pressed = SubResource("StyleBoxFlat_2a2dh")
TabContainer/fonts/font = ExtResource("1_2drlv")
TabContainer/styles/tab_focus = SubResource("StyleBoxFlat_5py8f")
TabContainer/styles/tab_hovered = SubResource("StyleBoxFlat_p38iw")
TabContainer/styles/tab_selected = SubResource("StyleBoxFlat_anaxc")
TabContainer/styles/tab_unselected = SubResource("StyleBoxFlat_otsmp")
VBoxContainer/constants/separation = 30
