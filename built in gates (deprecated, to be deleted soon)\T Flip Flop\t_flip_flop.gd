extends Gate2D

func custom_evaluate_expression():
	var res: Dictionary[String, bool] = {}

	var t = input_pins.get("t", false)

	var q = current_output_for_each_pin.get("q", false)

	if _is_positive_edge_triggered():
		if not t:
			res["q"] = q
		elif t:
			res["q"] = not q

		res["qn"] = not res["q"]

		for pin in output_pin_container.get_children():
			if pin.name in res:
				pin.state = res[pin.name]
				await pin.transfer_state()
		
		current_output_for_each_pin = res
	return res