[{"gate_name": "AND", "color": "(1, 1, 1, 1)", "gate_inputs": ["a", "b"], "gate_outputs": ["c"], "nodes": [{"id": "n1", "type": "AND", "inputs": ["a", "b"], "output": "c"}]}, {"gate_name": "OR", "color": "(1, 1, 1, 1)", "gate_inputs": ["a", "b"], "gate_outputs": ["c"], "nodes": [{"id": "n1", "type": "OR", "inputs": ["a", "b"], "output": "c"}]}, {"gate_name": "NOT", "color": "(1, 1, 1, 1)", "gate_inputs": ["a"], "gate_outputs": ["c"], "nodes": [{"id": "n1", "type": "NOT", "inputs": ["a"], "output": "c"}]}, {"gate_name": "NAND", "color": "(1, 1, 1, 1)", "gate_inputs": ["a", "b"], "gate_outputs": ["c"], "nodes": [{"id": "n1", "type": "AND", "inputs": ["a", "b"], "output": "w1"}, {"id": "n2", "type": "NOT", "inputs": ["w1"], "output": "c"}]}, {"gate_name": "NOR", "color": "(1, 1, 1, 1)", "gate_inputs": ["a", "b"], "gate_outputs": ["c"], "nodes": [{"id": "n1", "type": "OR", "inputs": ["a", "b"], "output": "w1"}, {"id": "n2", "type": "NOT", "inputs": ["w1"], "output": "c"}]}, {"gate_name": "XOR", "color": "(1, 1, 1, 1)", "gate_inputs": ["a", "b"], "gate_outputs": ["c"], "nodes": [{"id": "n1", "type": "XOR", "inputs": ["a", "b"], "output": "c"}]}, {"gate_name": "XNOR", "color": "(1, 1, 1, 1)", "gate_inputs": ["a", "b"], "gate_outputs": ["c"], "nodes": [{"id": "n1", "type": "XOR", "inputs": ["a", "b"], "output": "w1"}, {"id": "n2", "type": "NOT", "inputs": ["w1"], "output": "c"}]}]