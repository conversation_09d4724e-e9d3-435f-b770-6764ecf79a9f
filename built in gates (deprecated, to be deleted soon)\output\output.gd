class_name Output extends StaticBody2D

var state = false

@onready var pin: Pin2D = $"input pins/Pin 2D"
@onready var name_edit: LineEdit = $LineEdit

func _ready():
	pass

func _process(_delta):
	if not name_edit.text.is_empty():
		name = name_edit.text
	state = pin.state

func convert_prev_gate_to_node():
	if len(pin.get_connected_wires()) == 0:
		return null
	var input_pin = pin.get_connected_wires()[0].input_pin
	var gate = input_pin.get_parent().get_parent()
	if gate is Gate2D:
		# Create output swaps dictionary for this specific output
		var output_swaps = {}
		output_swaps[input_pin.name] = name

		var conversion_result = gate.convert_to_node(output_swaps)
		if conversion_result != null and conversion_result.has("output_names"):
			var output_names = conversion_result["output_names"]
			if output_names.has(input_pin.name):
				return output_names[input_pin.name]
			elif output_names.size() > 0:
				return output_names.values()[0]
	return null

func generate_boolean_expression_of_current_canvas():
	if len(pin.get_connected_wires()) == 0:
		return ""
	var input_pin = pin.get_connected_wires()[0].input_pin
	var gate = input_pin.get_parent().get_parent()
	if gate is Gate2D:
		return gate.generate_complete_boolean_expression()[input_pin.name]
	return ""
