class_name Output extends StaticBody2D

var state = false

@onready var pin: Pin2D = $"input pins/Pin 2D"
@onready var name_edit: LineEdit = $LineEdit

func _ready():
	pass

func _process(_delta):
	if not name_edit.text.is_empty():
		name = name_edit.text
	state = pin.state

func convert_prev_gate_to_node():
	if len(pin.get_connected_wires()) == 0:
		return null
	var input_pin = pin.get_connected_wires()[0].input_pin
	var gate = input_pin.get_parent().get_parent()
	if gate is Gate2D:
		var conversion_result = gate.convert_to_node(name)
		if conversion_result != null and conversion_result.has("output_name"):
			return conversion_result["output_name"]
	return null

func generate_boolean_expression_of_current_canvas():
	if len(pin.get_connected_wires()) == 0:
		return ""
	var input_pin = pin.get_connected_wires()[0].input_pin
	var gate = input_pin.get_parent().get_parent()
	if gate is Gate2D:
		return gate.generate_complete_boolean_expression()[input_pin.name]
	return ""
