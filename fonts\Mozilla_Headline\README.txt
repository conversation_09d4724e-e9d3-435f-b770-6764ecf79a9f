Mozilla Headline Variable Font
==============================

This download contains Mozilla Headline as both a variable font and static fonts.

Mozilla Headline is a variable font with these axes:
  wdth
  wght

This means all the styles are contained in a single file:
  Mozilla_Headline/MozillaHeadline-VariableFont_wdth,wght.ttf

If your app fully supports variable fonts, you can now pick intermediate styles
that aren’t available as static fonts. Not all apps support variable fonts, and
in those cases you can use the static font files for Mozilla Headline:
  Mozilla_Headline/static/MozillaHeadline_Condensed-ExtraLight.ttf
  Mozilla_Headline/static/MozillaHeadline_Condensed-Light.ttf
  Mozilla_Headline/static/MozillaHeadline_Condensed-Regular.ttf
  Mozilla_Headline/static/MozillaHeadline_Condensed-Medium.ttf
  Mozilla_Headline/static/MozillaHeadline_Condensed-SemiBold.ttf
  Mozilla_Headline/static/MozillaHeadline_Condensed-Bold.ttf
  Mozilla_Headline/static/MozillaHeadline_SemiCondensed-ExtraLight.ttf
  Mozilla_Headline/static/MozillaHeadline_SemiCondensed-Light.ttf
  Mozilla_Headline/static/MozillaHeadline_SemiCondensed-Regular.ttf
  Mozilla_Headline/static/MozillaHeadline_SemiCondensed-Medium.ttf
  Mozilla_Headline/static/MozillaHeadline_SemiCondensed-SemiBold.ttf
  Mozilla_Headline/static/MozillaHeadline_SemiCondensed-Bold.ttf
  Mozilla_Headline/static/MozillaHeadline-ExtraLight.ttf
  Mozilla_Headline/static/MozillaHeadline-Light.ttf
  Mozilla_Headline/static/MozillaHeadline-Regular.ttf
  Mozilla_Headline/static/MozillaHeadline-Medium.ttf
  Mozilla_Headline/static/MozillaHeadline-SemiBold.ttf
  Mozilla_Headline/static/MozillaHeadline-Bold.ttf
  Mozilla_Headline/static/MozillaHeadline_SemiExpanded-ExtraLight.ttf
  Mozilla_Headline/static/MozillaHeadline_SemiExpanded-Light.ttf
  Mozilla_Headline/static/MozillaHeadline_SemiExpanded-Regular.ttf
  Mozilla_Headline/static/MozillaHeadline_SemiExpanded-Medium.ttf
  Mozilla_Headline/static/MozillaHeadline_SemiExpanded-SemiBold.ttf
  Mozilla_Headline/static/MozillaHeadline_SemiExpanded-Bold.ttf
  Mozilla_Headline/static/MozillaHeadline_Expanded-ExtraLight.ttf
  Mozilla_Headline/static/MozillaHeadline_Expanded-Light.ttf
  Mozilla_Headline/static/MozillaHeadline_Expanded-Regular.ttf
  Mozilla_Headline/static/MozillaHeadline_Expanded-Medium.ttf
  Mozilla_Headline/static/MozillaHeadline_Expanded-SemiBold.ttf
  Mozilla_Headline/static/MozillaHeadline_Expanded-Bold.ttf

Get started
-----------

1. Install the font files you want to use

2. Use your app's font picker to view the font family and all the
available styles

Learn more about variable fonts
-------------------------------

  https://developers.google.com/web/fundamentals/design-and-ux/typography/variable-fonts
  https://variablefonts.typenetwork.com
  https://medium.com/variable-fonts

In desktop apps

  https://theblog.adobe.com/can-variable-fonts-illustrator-cc
  https://helpx.adobe.com/nz/photoshop/using/fonts.html#variable_fonts

Online

  https://developers.google.com/fonts/docs/getting_started
  https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Fonts/Variable_Fonts_Guide
  https://developer.microsoft.com/en-us/microsoft-edge/testdrive/demos/variable-fonts

Installing fonts

  MacOS: https://support.apple.com/en-us/HT201749
  Linux: https://www.google.com/search?q=how+to+install+a+font+on+gnu%2Blinux
  Windows: https://support.microsoft.com/en-us/help/314960/how-to-install-or-remove-a-font-in-windows

Android Apps

  https://developers.google.com/fonts/docs/android
  https://developer.android.com/guide/topics/ui/look-and-feel/downloadable-fonts

License
-------
Please read the full license text (OFL.txt) to understand the permissions,
restrictions and requirements for usage, redistribution, and modification.

You can use them in your products & projects – print or digital,
commercial or otherwise.

This isn't legal advice, please consider consulting a lawyer and see the full
license for all details.
