; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="Digital Electronics Simulator"
run/main_scene="uid://c17gwt0a7qnpp"
config/features=PackedStringArray("4.4", "Mobile")
config/icon="res://icon.svg"

[autoload]

SimulationManager="*res://simulation manager/SimulationManager.gd"
SaveSystem="*res://save and load/save system.gd"

[file_customization]

folder_colors={
"res://built in gates (deprecated, to be deleted soon)/": "red",
"res://built in gates (deprecated, to be deleted soon)/1-4 DEMUX/": "yellow",
"res://built in gates (deprecated, to be deleted soon)/3-PIN AND/": "orange",
"res://built in gates (deprecated, to be deleted soon)/4 BIT UP COUNTER/": "yellow",
"res://built in gates (deprecated, to be deleted soon)/4-1 MUX/": "yellow",
"res://built in gates (deprecated, to be deleted soon)/4-BIT PIPO REGISTER/": "yellow",
"res://built in gates (deprecated, to be deleted soon)/AND/": "orange",
"res://built in gates (deprecated, to be deleted soon)/CLK/": "orange",
"res://built in gates (deprecated, to be deleted soon)/D Flip Flop/": "yellow",
"res://built in gates (deprecated, to be deleted soon)/JK Flip Flop/": "yellow",
"res://built in gates (deprecated, to be deleted soon)/NAND/": "orange",
"res://built in gates (deprecated, to be deleted soon)/NOR/": "orange",
"res://built in gates (deprecated, to be deleted soon)/NOT/": "orange",
"res://built in gates (deprecated, to be deleted soon)/OR/": "orange",
"res://built in gates (deprecated, to be deleted soon)/SR Flip Flop/": "yellow",
"res://built in gates (deprecated, to be deleted soon)/SR Latch/": "yellow",
"res://built in gates (deprecated, to be deleted soon)/T Flip Flop/": "yellow",
"res://built in gates (deprecated, to be deleted soon)/XNOR/": "orange",
"res://built in gates (deprecated, to be deleted soon)/XOR/": "orange",
"res://built in gates (deprecated, to be deleted soon)/output/": "orange",
"res://built in gates (deprecated, to be deleted soon)/switch/": "orange",
"res://built in gates/": "red",
"res://canvas node/": "green",
"res://classes/": "teal",
"res://fonts/": "blue",
"res://gate library/": "purple",
"res://save and load/": "pink"
}

[global_group]

pin2d=""
switch=""
output=""
gate2d=""
clk=""
special_gates=""
wire2d=""

[rendering]

renderer/rendering_method="mobile"
environment/defaults/default_clear_color=Color(0.0142752, 0.0142752, 0.0142752, 1)
